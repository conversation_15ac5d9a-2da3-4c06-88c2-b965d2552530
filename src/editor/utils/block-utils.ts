/**
 * Utility functions for working with program blocks and metadata
 * 
 * This module provides reusable functions for searching and manipulating
 * program blocks, statements, and metadata entries.
 */

import { Block, ProgramStatement } from '@/vpl/program';

/**
 * Recursively searches for a metadata entry in a block by UUID
 * @param block The block to search in
 * @param targetUuid The UUID to search for
 * @returns The found statement or null if not found
 */
export function findMetadataEntry(block: any[], targetUuid: string): any {
  const directEntry = block.find(stmt => stmt._uuid === targetUuid);
  if (directEntry) return directEntry;

  for (const stmt of block) {
    if (stmt.block && Array.isArray(stmt.block)) {
      const nestedEntry = findMetadataEntry(stmt.block, targetUuid);
      if (nestedEntry) return nestedEntry;
    }
  }
  return null;
}

/**
 * Recursively searches for a block/statement by UUID
 * @param block The block to search in
 * @param targetUuid The UUID to search for
 * @returns The found statement or null if not found
 */
export function findBlockRecursively(block: any[], targetUuid: string): any {
  for (const stmt of block) {
    if (stmt._uuid === targetUuid) {
      return stmt;
    }
    if (stmt.block && Array.isArray(stmt.block)) {
      const found = findBlockRecursively(stmt.block, targetUuid);
      if (found) return found;
    }
  }
  return null;
}
