{
  "compilerOptions": {
    "target": "ES2020",
    "experimentalDecorators": true,
    "useDefineForClassFields": false,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": false,

    /* Bundler mode */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,

    /* Aliases */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/editor/components/*"],
      "@assets/*": ["src/assets/*"],
      "@vpl/*": ["src/vpl/*"]
    },
    "plugins": [
      {
        "name": "ts-lit-plugin"
      }
    ]
  },
  "include": ["src/**/*"]
}
