{"name": "@pocketix/vpl-for-things", "type": "module", "version": "1.0.6", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}], "license": "MIT", "private": false, "files": ["dist"], "main": "./dist/vpl-for-things.umd.cjs", "module": "./dist/lit-project.js", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/vpl-for-things.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/vpl-for-things.umd.cjs"}}, "./style.css": "./dist/style.css", "./package.json": "./package.json"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "build:watch": "tsc && vite build --watch", "preview": "vite preview", "release": "standard-version && git push --follow-tags && npm publish", "prepare": "npm run build"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "repository": "https://github.com/pocketix/vpl-for-things", "dependencies": {"@lit/context": "^1.1.0", "lit": "^3.1.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.11.19", "standard-version": "^9.5.0", "ts-lit-plugin": "^2.0.2", "typescript": "^5.2.2", "vite": "^6.3.3", "vite-plugin-dts": "^4.5.3"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.36.0"}}