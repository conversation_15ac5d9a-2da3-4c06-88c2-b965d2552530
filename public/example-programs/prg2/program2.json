{"header": {"userVariables": {}, "userProcedures": {}}, "block": [{"id": "if", "block": [{"id": "LT22222-<PERSON><PERSON>-1.<PERSON><PERSON><PERSON><PERSON>", "arguments": [{"type": "str_opt", "value": "open"}]}], "arguments": [{"type": "boolean_expression", "value": [{"value": [{"value": [{"type": "variable", "value": "DistanceSensor-1.waterLevel"}, {"type": "number", "value": 80}], "type": ">"}, {"value": [{"type": "variable", "value": "LT22222-Relay-1.relayState"}, {"type": "string", "value": "closed"}], "type": "==="}], "type": "&&"}]}]}, {"id": "elseif", "block": [{"id": "LT22222-<PERSON><PERSON>-1.<PERSON><PERSON><PERSON><PERSON>", "arguments": [{"type": "str_opt", "value": "close"}]}], "arguments": [{"type": "boolean_expression", "value": [{"value": [{"type": "variable", "value": "LT22222-Relay-1.relayState"}, {"type": "string", "value": "opened"}], "type": "==="}]}]}]}