{"header": {"userVariables": {"myPhoneNumber": {"type": "string", "value": "+420123456789"}, "alertMessage": {"type": "string", "value": "Movement detected!"}}, "userProcedures": {}}, "block": [{"id": "if", "block": [{"id": "alert", "arguments": [{"type": "str_opt", "value": "phone_number"}, {"type": "variable", "value": "myPhoneNumber"}, {"type": "variable", "value": "alertMessage"}]}, {"id": "Doorbell-1.takePicture"}], "arguments": [{"type": "boolean_expression", "value": [{"value": [{"type": "string", "value": "active"}, {"type": "variable", "value": "Doorbell-1.motion<PERSON><PERSON><PERSON>"}], "type": "==="}]}]}]}