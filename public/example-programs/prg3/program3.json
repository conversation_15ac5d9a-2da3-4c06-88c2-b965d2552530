{"header": {"userVariables": {}, "userProcedures": {"buzzerBeep3x": [{"id": "repeat", "block": [{"id": "Buzzer-1.beep"}], "arguments": [{"type": "number", "value": 3}]}]}}, "block": [{"id": "switch", "block": [{"id": "case", "block": [{"id": "LED-1.setLedColor", "arguments": [{"type": "str_opt", "value": "red"}]}], "arguments": [{"type": "string", "value": "high"}]}, {"id": "case", "block": [{"id": "LED-1.setLedColor", "arguments": [{"type": "str_opt", "value": "orange"}]}], "arguments": [{"type": "string", "value": "ideal"}]}, {"id": "case", "block": [{"id": "LED-1.setLedColor", "arguments": [{"type": "str_opt", "value": "green"}]}], "arguments": [{"type": "string", "value": "low"}]}, {"id": "case", "block": [{"id": "LED-1.setLedColor", "arguments": [{"type": "str_opt", "value": "blue"}]}, {"id": "buzzerBeep3x"}, {"id": "TemperatureDevice-1.setTemperature", "arguments": [{"type": "str_opt", "value": "ideal"}]}], "arguments": [{"type": "string", "value": "critical_low"}]}], "arguments": [{"type": "variable", "value": "TemperatureDevice-1.temperatureLevel"}]}]}