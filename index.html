<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VPL For Things</title>

    <link rel="stylesheet" href="./src/index.css" />
    <script type="module" src="./src/index.ts"></script>

    <!-- <script src="./dist/vpl-for-things.umd.cjs"></script>
    <link rel="stylesheet" href="./dist/style.css" /> -->

    <style>
      body {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        min-height: 100vh;
        margin: 0;
        padding-left: 0.5rem;
        padding-right: 0.5rem;
      }
    </style>
  </head>
  <body>
    <vpl-editor></vpl-editor>
  </body>
</html>
