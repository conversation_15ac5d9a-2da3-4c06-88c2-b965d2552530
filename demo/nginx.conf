server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /dist/ {
        root /usr/share/nginx/html;
    }

    error_page 404 /index.html;

    location ~* \.(?:ico|css|js|gif|jpe?g|png|woff2?|eot|ttf|otf|svg|mp4|webm|ogg|mp3|wav|flac|aac)$ {
        expires 6M;
        access_log off;
        add_header Cache-Control "public";
    }
}
